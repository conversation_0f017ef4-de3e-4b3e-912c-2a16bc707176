use candle_core::Tensor;
use candle_nn::{ops::softmax, Init, VarBuilder, VarMap};

#[derive(Clone)]
pub struct MatmulLayer {
    pub dataset_var: DatasetVar,
}

#[derive(Clone, Debug)]
pub struct DatasetVar {
    pub weight: Tensor,
}

impl DatasetVar {
    fn dataset_var(dataset: Tensor, vb: VarBuilder) -> anyhow::Result<DatasetVar> {
        
        let weight = vb.get_with_hints(
            dataset.dims(),
            "dataset_weight",
            candle_nn::Init::Randn { mean: 0.5, stdev: 0.25 } // This will be overwritten
        )?;
        
        Ok(DatasetVar { weight })
    }
}

impl MatmulLayer {
    pub fn load(vb: VarBuilder, dataset: Tensor) -> anyhow::Result<Self> {
        let dataset_var = DatasetVar::dataset_var(dataset, vb.clone())?;
        Ok(Self { dataset_var })
    }

    pub fn init(&self, dataset: Tensor, mut vm: VarMap) -> anyhow::Result<VarMap> {
        vm.set_one("dataset_weight".to_string(), dataset)?;
        Ok(vm)
    }

    pub fn forward(&self, input_tensor: &Tensor) -> candle_core::Result<(Tensor,Tensor)> {
        let input_reshaped = input_tensor.reshape(input_tensor.shape().clone().extend( &[1usize]))?;
        let probs = self.dataset_var.weight.matmul(&input_reshaped)?;
        let norm_probs = softmax(&probs, 0)?;
        Ok((norm_probs.t()?.matmul(&self.dataset_var.weight)?,norm_probs))
    }
}
