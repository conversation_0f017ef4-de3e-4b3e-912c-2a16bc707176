use candle_core::Tensor;

fn main() -> anyhow::Result<()> {
    println!("loading MNIST dataset...");
    let mnist = candle_datasets::vision::mnist::load()?;
    let random_t = mnist.train_images.get_on_dim(0,0)?.rand_like(0f64,1f64)?;
    let random_t = random_t.reshape(random_t.shape().clone().extend( &[1usize]))?;
    println!("random_t shape: {:?}", random_t.shape());

    let train_images = mnist.train_images;
    let probs = train_images.matmul(&random_t)?;
    println!("probs shape: {:?}", probs.shape());

    println!("probs: {:?}", probs);
    


    Ok(())
}
