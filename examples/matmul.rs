use candle_core::Tensor;
use candle_nn::ops::{log_softmax, softmax};
use image::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>};

fn main() -> anyhow::Result<()> {
    println!("loading MNIST dataset...");
    let mnist = candle_datasets::vision::mnist::load()?;
    let index = rand::random::<u32>() % mnist.train_images.dims()[1] as u32;
    println!("index: {}", index);
    let image_t = mnist.train_images.get_on_dim(0,index as usize)?;
    let random_t = image_t.reshape(image_t.shape().clone().extend( &[1usize]))?;
    println!("random_t shape: {:?}", random_t.shape());

    let train_images = mnist.train_images;
    println!("train_images shape: {:?}", train_images.shape());
    let probs = train_images.matmul(&random_t)?;

    // linear
    //let norm_probs = probs;

    // Softmax
    let norm_probs = softmax(&probs, 0)?;
    let mut v: Vec<f32> = norm_probs.squeeze(1)?.to_vec1()?;
    v.sort_by(|a, b| a.partial_cmp(b).unwrap());
    println!("softmax: {:?}", v.iter().rev().take(10).collect::<Vec<_>>());


    // /max
    //let max_val = probs.max_all()?;
    //let norm_probs = probs.broadcast_div(&max_val)?;

    let pred_t = norm_probs.t()?.matmul(&train_images)?;

    println!("type of pred_t: {:?}", pred_t.dtype());
    println!("type of random_t: {:?}", random_t.dtype());
    let diff = pred_t.squeeze(0)?.sub(&random_t.squeeze(1)?)?;

    tensor_to_image(&random_t,"random_t")?;
    tensor_to_image(&pred_t,"pred_t")?;
    
    println!("difference: {}",diff.to_vec1()?.iter().sum::<f32>());


    Ok(())
}


fn tensor_to_image(tensor: &Tensor,name: &str) -> anyhow::Result<()> {
    // Reshape from [784] to [28, 28]
    let image_tensor = tensor.reshape((28, 28))?;
    let max_val = image_tensor.max_all()?;
    let image_tensor = image_tensor.broadcast_div(&max_val)?;
    
    // Convert to Vec and normalize to [0, 255]
    let data: Vec<Vec<f32>> = image_tensor.to_vec2()?;
    // Create image buffer
    let mut img_buffer = ImageBuffer::new(28, 28);
    
    for (y, row) in data.iter().enumerate() {
        for (x, &pixel) in row.iter().enumerate() {
            // Assuming pixel values are in [0, 1] range
            let pixel_u8 = (pixel.clamp(0.0, 1.0) * 255.0) as u8;
            img_buffer.put_pixel(x as u32, y as u32, Luma([pixel_u8]));
        }
    }
    
    // Save image
    img_buffer.save(format!("{name}.png"))?;
    
    Ok(())
}